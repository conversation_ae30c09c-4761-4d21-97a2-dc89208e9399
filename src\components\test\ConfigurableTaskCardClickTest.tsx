'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useTaskSelectionState, useTaskSelectionActions, TaskSelectionProvider } from '@/contexts/TaskSelectionContext';
import { ConfigurableTaskCard } from '@/components/sections/task-list/cards/ConfigurableTaskCard';
import { defaultTaskCardConfig } from '@/types/taskCardConfig';
import type { Task, Vehicle } from '@/types';

const ConfigurableTaskCardClickTestContent: React.FC = () => {
  const { selectedTask, selectedTaskId, isTaskSelected } = useTaskSelectionState();
  const { setSelectedTask, clearSelection } = useTaskSelectionActions();

  // 模拟任务数据
  const testTasks: Task[] = [
    {
      id: 'task-1',
      taskNumber: 'T001',
      projectName: '测试项目A',
      dispatchStatus: 'pending',
      isDueForDispatch: false,
      requiredVolume: 100,
      completedVolume: 0,
      scheduledTime: new Date(),
      contactPhone: '13800138001',
      completedProgress: 0,
      estimatedDuration: 120,
      constructionLocation: '工地A',
      taskStatus: 'active',
      customerName: '客户A',
      createdAt: new Date(),
      updatedAt: new Date(),
      productionLineCount: 3,
      hasNewMessages: true,
      unreadMessageCount: 2,
      concreteStrength: 'C30',
      dispatchReminder: {
        timeRemaining: 1800, // 30分钟
        isOverdue: false,
        isCalculating: false
      }
    },
    {
      id: 'task-2', 
      taskNumber: 'T002',
      projectName: '测试项目B',
      dispatchStatus: 'in_progress',
      isDueForDispatch: true,
      requiredVolume: 200,
      completedVolume: 80,
      scheduledTime: new Date(),
      contactPhone: '13800138002',
      completedProgress: 40,
      estimatedDuration: 180,
      constructionLocation: '工地B',
      taskStatus: 'active',
      customerName: '客户B',
      createdAt: new Date(),
      updatedAt: new Date(),
      productionLineCount: 2,
      hasNewMessages: false,
      unreadMessageCount: 0,
      concreteStrength: 'C25',
      dispatchReminder: {
        timeRemaining: -600, // 超时10分钟
        isOverdue: true,
        isCalculating: false
      }
    },
    {
      id: 'task-3',
      taskNumber: 'T003', 
      projectName: '测试项目C',
      dispatchStatus: 'completed',
      isDueForDispatch: false,
      requiredVolume: 150,
      completedVolume: 150,
      scheduledTime: new Date(),
      contactPhone: '13800138003',
      completedProgress: 100,
      estimatedDuration: 90,
      constructionLocation: '工地C',
      taskStatus: 'completed',
      customerName: '客户C',
      createdAt: new Date(),
      updatedAt: new Date(),
      productionLineCount: 1,
      hasNewMessages: false,
      unreadMessageCount: 0,
      concreteStrength: 'C35',
      dispatchReminder: {
        timeRemaining: 0,
        isOverdue: false,
        isCalculating: false
      }
    }
  ];

  // 模拟车辆数据
  const testVehicles: Vehicle[] = [
    {
      id: 'vehicle-1',
      vehicleNumber: 'V001',
      assignedTaskId: 'task-1',
      status: 'dispatched',
      driverName: '司机A',
      capacity: 10
    },
    {
      id: 'vehicle-2',
      vehicleNumber: 'V002', 
      assignedTaskId: 'task-2',
      status: 'loading',
      driverName: '司机B',
      capacity: 12
    }
  ];

  const handleTaskClick = (task: Task, e: React.MouseEvent) => {
    console.log('=== CONFIGURABLE CARD CLICK TEST ===');
    console.log('Task:', task.taskNumber);
    
    // 切换选中状态
    const isCurrentlySelected = isTaskSelected(task.id);
    console.log('Currently selected:', isCurrentlySelected);

    if (isCurrentlySelected) {
      console.log('Deselecting...');
      setSelectedTask(null);
    } else {
      console.log('Selecting...');
      setSelectedTask(task);
    }
    console.log('=== END CONFIGURABLE CARD CLICK TEST ===');
  };

  // 模拟操作栏按钮状态
  const hasSelectedTask = !!selectedTaskId;

  return (
    <div className="p-6 space-y-6">
      <div className="space-y-4">
        <h2 className="text-2xl font-bold">ConfigurableTaskCard 单击选中测试</h2>
        
        {/* 选中状态显示 */}
        <div className="p-4 bg-gray-50 rounded-lg">
          <h3 className="font-semibold mb-2">当前选中状态:</h3>
          <div className="space-y-2">
            <p>选中任务ID: <Badge variant="outline">{selectedTaskId || '无'}</Badge></p>
            <p>选中任务编号: <Badge variant="outline">{selectedTask?.taskNumber || '无'}</Badge></p>
            <p>选中任务项目: <Badge variant="outline">{selectedTask?.projectName || '无'}</Badge></p>
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex gap-2">
          <Button 
            onClick={clearSelection}
            variant="outline"
            disabled={!hasSelectedTask}
          >
            清除选择
          </Button>
          <Button 
            disabled={!hasSelectedTask}
            variant={hasSelectedTask ? "default" : "secondary"}
          >
            发车 {hasSelectedTask && `(${selectedTask?.taskNumber})`}
          </Button>
          <Button 
            disabled={!hasSelectedTask}
            variant={hasSelectedTask ? "destructive" : "secondary"}
          >
            撤销任务 {hasSelectedTask && `(${selectedTask?.taskNumber})`}
          </Button>
        </div>

        {/* 状态说明 */}
        <div className="p-3 bg-blue-50 rounded-lg">
          <p className="text-sm text-blue-700">
            {hasSelectedTask 
              ? `按钮已启用，将操作任务: ${selectedTask?.taskNumber}` 
              : '所有按钮已禁用，请先选择一个任务'
            }
          </p>
        </div>
      </div>

      {/* 任务卡片网格 */}
      <div className="space-y-3">
        <h4 className="font-medium">ConfigurableTaskCard 组件 (点击选中/取消选中):</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {testTasks.map(task => {
            const taskVehicles = testVehicles.filter(v => v.assignedTaskId === task.id);
            
            return (
              <ConfigurableTaskCard
                key={task.id}
                task={task}
                vehicles={taskVehicles}
                config={defaultTaskCardConfig}
                size="small"
                onTaskClick={handleTaskClick}
                onTaskContextMenu={(e, task) => {
                  e.preventDefault();
                  console.log('Context menu for task:', task.taskNumber);
                }}
                onTaskDoubleClick={(task) => {
                  console.log('Double click for task:', task.taskNumber);
                }}
              />
            );
          })}
        </div>
      </div>

      {/* 使用说明 */}
      <div className="p-4 bg-yellow-50 rounded-lg">
        <h4 className="font-medium mb-2">测试说明:</h4>
        <ul className="text-sm text-gray-700 space-y-1">
          <li>• 点击任务卡片可以选中/取消选中</li>
          <li>• 选中状态会显示在顶部状态区域</li>
          <li>• 操作按钮会根据选中状态启用/禁用</li>
          <li>• 点击卡片内的按钮、消息图标等不会触发选中</li>
          <li>• 选中的卡片会有特殊的背景色、边框和阴影样式</li>
          <li>• 支持右键菜单和双击事件</li>
        </ul>
      </div>
    </div>
  );
};

export const ConfigurableTaskCardClickTest: React.FC = () => {
  return (
    <TaskSelectionProvider>
      <ConfigurableTaskCardClickTestContent />
    </TaskSelectionProvider>
  );
};

export default ConfigurableTaskCardClickTest;
